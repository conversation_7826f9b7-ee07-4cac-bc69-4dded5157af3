<template>
  <div class="compliance-review-container">
    <!-- 顶部导航区域 -->
    <div class="header-section">
      <div class="breadcrumb-area">
        <div class="nav-buttons">
          <a-button type="text" class="nav-btn back-btn" @click="goHome">
            <template #icon>
              <CornerUpLeft class="icon" :size="16"/>
            </template>
            首页
          </a-button>
          <a-button type="text" class="nav-btn history-btn"@click="showHistoryFiles">
            <template #icon>
              <Clock8 class="icon" :size="16"/>
            </template>
          </a-button>
        </div>
        <div class="file-name">{{ isSkeleton ? simpleData.fileName : statsData.fileName }}</div>
      </div>
      
      <div class="info-actions">
        <div class="review-time">
          <ClockFading  class="icon" :size="16"/>
          <span>解析完成时间：{{ statsData.analysisFinishTime  || '-' }}</span> 
        </div>
        <div class="review-time">
          <Calendar1  class="icon" :size="16"/> 
          <span>审查时间：{{ statsData.reviewTime   || '-'}}</span>
        </div>
        <div class="action-buttons">
          <a-dropdown
            v-model:open="exportState.visible"
            :trigger="['click']"
            @openChange="handleExportDropdownChange"
            placement="bottomRight"
          >
            <a-button class="export-btn">
              <template #icon>
                <Download class="icon" :size="16"/>
              </template>
              导出
              <DownOutlined />
            </a-button>
            <template #overlay>
              <div class="export-dropdown-content">
                <div class="export-options">
                  <div  v-for="option in exportOptionsList"  :key="option.key" class="export-option">
                    <a-checkbox v-model:checked="exportState.options[option.key]">
                      {{ option.label }}
                    </a-checkbox>
                  </div>
                </div>
                <div class="export-actions">
                  <a-button size="small" @click="cancelExport">取消</a-button>
                  <a-button
                    type="primary"
                    size="small"
                    :loading="exportState.loading"
                    :disabled="!hasSelectedOptions || isSkeleton"
                    @click="confirmExport"
                  >
                    导出
                  </a-button>
                </div>
              </div>
            </template>
          </a-dropdown>
          <a-button type="primary" @click="showCheckList">
            查看审查清单
          </a-button>
        </div>
      </div>
    </div>
    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- PDF阅读器区域 -->  
      <div class="pdf-reader-wrapper">
        <template v-if="pdfData.pdfUrl"> 
          <PdfReader
            v-if="isPdf"
            :url="pdfData.pdfUrl"
            :page="pdfData.currentPage" 
            :rect="pdfData.highlightRects"  
          />
          <KkfileReader v-else :url="pdfData.pdfUrl"/>  
        </template> 
      </div> 

      <!-- 审查结果面板 -->
      <div class="review-panel" ref="review-panel">
        <div class="panel-header">审查结果</div>
        <!-- 筛选标签 - 加载时显示骨架 -->
        <div class="filter-tabs"> 
          <div
            v-for="tab in filterTabs"
            :key="tab.key +'1'"
            :class="['filter-tab', tab.key, { active: state.activeFilter === tab.key }]"
            @click="setActiveFilter(tab.key)"
          >
            <span class="tab-label">{{ tab.label }}</span>
            <span class="tab-count" :class="[{ active: state.activeFilter === tab.key }]">{{ isSkeleton ? 0 : (tab.count || 0) }}</span>
          </div> 
        </div>

        <!-- 审查项目列表 -->
        <div class="review-items"> 
          <!-- 骨架屏状态 -->
          <div v-if="isSkeleton || state.loading" class="skeleton-container">
            <div
              v-for="(category, index) in SKELETON_CONFIG.categories"
              :key="index"
              class="skeleton-item-group"
            >
              <div class="skeleton-title-bar">
                <span class="item-index"></span>
                <span class="item-title">{{ category.name }}</span>
              </div>
              <div class="skeleton-content">
                <div
                  v-for="n in category.itemCount"
                  :key="n"
                  class="skeleton-review-item"
                >
                  <div class="skeleton-line skeleton-line-long"></div>
                  <div class="skeleton-line skeleton-line-medium"></div>
                  <div class="skeleton-line skeleton-line-short"></div>
                </div>
              </div>
            </div>
          </div> 
          <!-- 有数据内容 -->
          <div v-else-if="filteredItems.length && resultData.reviewResult !== -1" class="items-list">
            <div
              v-for="item in filteredItems"
              :key="item.reviewItemCode"
              class="item-group"
            >
              <div class="item-title-bar">
                <span class="item-index"></span>
                <span class="item-title">{{ item.reviewItemName }}</span>
                <span class="item-count">{{ item.pointNum || item.children?.length || 0 }}</span>
              </div>
              <!-- 审查项目列表 -->
              <div class="sub-items">
                <ReviewItem
                  v-for="subItem in item.children || []"
                  :key="subItem.uniqueId"
                  :data="subItem"
                  :active="activeItem.uniqueId"
                  :task-id="taskId"
                  @updateFinishNum="val => { statsData.resultFinishNum += val }"
                  @clickItem="handleReviewItemClick"
                  @update:data="val => Object.assign(subItem, val)"
                />
              </div>
            </div>
          </div>  
          <BaseEmpty v-else description="暂无数据" />
        </div>
        <!--  已处理         -->
        <div class="handle-result">
          <span>已处理</span> <span class="num">{{statsData.resultFinishNum || 0}}/{{ statsData.resultNum || 0 }}</span> <span>风险点</span>
          <div class="percent-bar"><span class="percent" :style="{width: resultBarWidth }"></span></div>
        </div>
      </div>
    </div>

    <!-- 审查清单弹窗 -->
    <CheckListModal
      v-model:open="state.checkListVisible"
      :task-id="taskId"
      :isSkeleton="isSkeleton"
      @save="handleCheckListSave"
    />

    <!-- 历史文件弹窗 -->
    <HistoryFilesModal
      v-model="state.historyFilesVisible"
      :task-id="taskId"
      placement="left"
      :filteredItems="filteredItems"
      @preview="handleFilePreview"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { DownOutlined } from '@ant-design/icons-vue'
import { CornerUpLeft, Clock8, Calendar1, Download, ClockFading } from 'lucide-vue-next'
import { usePolling } from '@/hooks/use-polling'
import { SKELETON_CONFIG, createFilterTabs, DEFAULT_REVIEW_RESULT, exportOptionsList} from '@/views/hooks/examine'
import { useExport } from '@/views/hooks/use-export'
import { getTaskReview, apiGetFile, complianceTaskGetOne } from '@/api/examine'
import PdfReader from '@/components/PdfReader/index.vue'
import BaseEmpty from '@/components/BaseEmpty/index.vue'
import CheckListModal from './components/CheckListModal.vue'
import HistoryFilesModal from './components/HistoryFilesModal.vue'
import ReviewItem from './components/ReviewItem.vue'
import KkfileReader from '@/components/KkfileReader/index.vue' 


defineOptions({
  name: 'ComplianceReview'
})

const router = useRouter()
const route = useRoute() 

// 获取任务ID
const taskId = ref(route.query.taskId as string || '')
// 骨架屏显示逻辑：当 taskStatus 不是 -1 或 2 时显示骨架屏
const isSkeleton = computed(() => simpleData.value.taskStatus !== -1 && simpleData.value.taskStatus !== 2 )
// 页面状态管理
const state = reactive({
  loading: false,
  activeFilter: null as number | null,
  checkListVisible: false,
  historyFilesVisible: false
})

// 统计数据
const statsData = ref<Record<string, any>>({})

// PDF相关数据
const pdfData = reactive({
  pdfUrl: '',
  currentPage: 1,
  highlightRects: [] as any[]
})

// 审查结果数据
const resultData = reactive<Record<string, any>>({ ...DEFAULT_REVIEW_RESULT })

// 当前选中的审查项
const activeItem = ref<Record<string, any>>({}) 

// 判断是否为PDF文件
const isPdf = computed(() => {
  if (!pdfData.pdfUrl) return false
  return pdfData.pdfUrl.toLowerCase().endsWith('.pdf')
})
const resultBarWidth = computed(()=> {
  if(statsData.value.resultFinishNum && statsData.value.resultNum) {
    const percentage = (statsData.value.resultFinishNum / statsData.value.resultNum * 100).toFixed(2)
    return `${percentage}%`
  }
  else return '0%'
})

// 筛选标签
const filterTabs = computed(() => createFilterTabs(statsData.value))

// 过滤后的审查项目
const filteredItems = computed(() => {
  const dataList = resultData.dataList || []
  // 按 reviewItemCode 分类
  return dataList.reduce((acc, item) => {
    let group = acc.find(group => group.reviewItemCode === item.reviewItemCode)
    if (!group) {
      group = {
        reviewItemCode: item.reviewItemCode,
        reviewItemName: item.reviewItemName,
        children: []
      }
      acc.push(group)
    }
    group.children.push(item)
    return acc
  }, [] as { reviewItemCode: string; reviewItemName: string; children: any[] }[])
})  

// 点击审查项处理
const handleReviewItemClick = (item: any) => {
  activeItem.value = item ?? {} 
  pdfData.highlightRects = item.position ?? []
  pdfData.currentPage = -1
  if (item.page) {
    nextTick(() => {
      pdfData.currentPage = item.page
    })
  }
  // nextTick(() => { 
    // pdfData.currentPage = item.page
    // pdfData.highlightRects = item?.position?.map(pos => ({
    //   page: pos.page,
    //   x1: pos.x1,
    //   x2: pos.x2,
    //   y1: pos.y1,
    //   y2: pos.y2,
    //   // text: item.handleStatus === 1 ? item.revisionSuggestion || '' : '',
    //   id: item.uniqueId
    // })) ?? []
  // })
}

// 获取审查数据
const isOnlyReviewData = ref(false)
const getData = async () => { 
  state.loading = true 
  Object.assign(resultData, DEFAULT_REVIEW_RESULT)
  const { data, err } = await getTaskReview({
    taskId: taskId.value,
    reviewResult: state.activeFilter
  })
  state.loading = false
  if (err) return 
  const stats = data?.stats ?? {}
  if (!isOnlyReviewData.value) {
    statsData.value = {
      resultFinishNum: 0,
      ...stats, 
      finalFileId: data.finalFileId,
      fileName: data.fileName,
      reviewTime: data.reviewTime,
      analysisFinishTime : data.analysisFinishTime
    }
    isOnlyReviewData.value = false
  }
  Object.assign(resultData, DEFAULT_REVIEW_RESULT, data)

}
// ==================== 业务方法 ====================

// 导航方法
const goHome = () => {
  router.push({ name: 'HomeIndex' })
}

// 获取文件URL
const getFile = async () => {
  const finalFileId = statsData.value.finalFileId || simpleData.value.finalFileId
  if (!finalFileId) {
    message.info('缺少文件ID')
    return
  }
  pdfData.currentPage = 1
  pdfData.highlightRects = []
  pdfData.pdfUrl = ''
  const { data, err } = await apiGetFile(finalFileId)
  if (err) return
  pdfData.pdfUrl = data.fileUrl
}

// ==================== 导出相关方法 ====================
const {  
  state: exportState, 
  hasSelectedOptions, 
  cancel: cancelExport, 
  show:showExport,
  confirm: confirmExport 
} = useExport(exportOptionsList, taskId.value)
const handleExportDropdownChange = (open: boolean) => {
  if (open) {
    showExport() // 打开时重置为全选
  }
}
// ==================== 弹窗控制方法 ====================

// 显示审查清单
const showCheckList = () => {
  state.checkListVisible = true
}

// 显示历史文件
const showHistoryFiles = () => {
  state.historyFilesVisible = true
}

// 文件预览处理
const handleFilePreview = (file: any) => {
  taskId.value = file.taskId 
  router.push({
    name: 'ComplianceReview',
    query: {taskId: file.taskId}
  })
  simpleData.value = {}
  // 重新开始轮询
  restart() 
}

// 审查清单保存处理
const handleCheckListSave = (checkList: any) => {
  console.log('保存审查清单:', checkList)
  message.success('审查清单已保存')
}

// 设置筛选条件
const setActiveFilter = (filterKey: number | null) => {
  if(isSkeleton.value) return
  state.activeFilter = filterKey
  // 只获取审查数据，不重新获取统计数据，保持统计数量不变
  getData()
} 
// ==================== 轮询 ====================
let isInitialized = false
const simpleData = ref<any>({})
const { start, stop, restart } = usePolling(
  async () => {
    const { data, err } = await complianceTaskGetOne({ taskId: taskId.value })
    if (err) return
    return data
  },
  {
    interval: 5*60*1000,
    requestTimeout: 10000, // 10秒超时
    skipOnPending: true, // 跳过进行中的请求
    maxConcurrent: 1, // 最大并发数
    onSuccess:async (data) => {
      console.log('轮询成功:', data) 
      if (data) {
        simpleData.value = data ?? {} 
        // 检查 taskStatus，如果为 -1 或 2 则停止轮询并更新数据
        if (data.taskStatus === -1 || data.taskStatus === 2) {
          console.log('任务完成，停止轮询，taskStatus:', data.taskStatus)
          stop()
          refreshData()
        }else if(!isInitialized) {
          isInitialized = true
          await getFile()
        }
      }
    },
    onError: (error, count) => {
      console.error(`轮询失败 (${count}次):`, error)
    }
  }
)

// 刷新数据的方法
const refreshData = async () => {
  await getData()
  await getFile()
}
start()
</script>

<style lang="scss" scoped>
.compliance-review-container {
  color: #111827;
  height: 100vh;
  overflow:hidden;
  display:flex;
  flex-direction:column;
}

.header-section {
  flex-shrink:0;
  display: flex;
  align-items: center;
  justify-content: space-between; 
  padding: 11px 24px;
  border-bottom: 1px solid var(--line-2); 
  box-sizing:border-box;
  .breadcrumb-area {
    display: flex;
    align-items: center;  
    .nav-buttons {
      display: flex;
      gap: 16px;
      .nav-btn {
        display: flex;
        align-items: center; 
        &.back-btn { 
          padding: 8px 16px;
          border: 1px solid var(--line-3);
          border-radius: 4px;
          .icon {
            margin-right: 8px;
          }
        }
        &.history-btn{
          border: 1px solid var(--line-3);
          padding: 8px;
           margin-right: 16px;
          &:hover {
            background-color: transparent;
          }
        }
      }
    } 
  }
  
  .info-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    .review-time {
      display: flex;
      align-items: center;
      gap: 10px;
      color: #4B5563;
    }
    
    .action-buttons {
      display: flex;
      gap: 10px;

      .export-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        border: 1px solid var(--line-3);

        .icon {
          margin-right: 4px;
        }
      }
    }
  }
}

.main-content {
  display: flex; 
  flex:1;
  min-height:0;

} 

.pdf-reader-wrapper { 
  border-right:1px solid #E5E7EB;
  position: relative;
  flex: 1;
  min-width: 300px; 
  overflow-y:auto;
  .pdf-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
} 
.review-panel {  
  position: relative; 
  flex:1;
  max-width: 832px;
  overflow-y:auto;
  display: flex;
  flex-direction: column;
  background: var(--fill-0);  
  .handle-result {
    position: sticky;
    display: flex;
    align-items: center;
    bottom: 0;
    height: 45px;
    line-height: 45px;
    padding: 0 12px;
    z-index: 10;
    background-color: var(--fill-0);
    box-shadow: 0px -2px 4px -2px #0000001A;
    color: #374151;
    margin-top: auto;
    .num {
      color: var(--main-6);    
      flex-shrink: 0;      
    }
    .text {
      flex-shrink: 0;
    }
    .percent-bar {
      flex: 1;
      min-width: 0;
      margin-left: 6px;
      background-color: #E5E7EB;
      height: 8px;
      display: flex;
      border-radius: 8px;
      .percent {
        border-radius: 8px;
        background-color: var(--main-6);
      }
    }
  }
  .panel-header {
    padding: 16px; 
    font-size: 16px;
  }
  
  .filter-tabs { 
    display: flex; 
    align-items: center; 
    gap: 4px; 
    padding: 4px;
    background-color: #F5F5F5;
    border-radius: 4px;
    margin:0 16px 16px 16px;
    .filter-tab {
      display: flex;
      align-items: center; 
      justify-content: center;
      flex: 1;
      height: 38px;
      min-width: 60px; 
      border-radius: 4px;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s; 
      .tab-label {
        font-size: var(--font-14);
        color: var(--text-4);
      }
      .tab-count {
        display: inline-block;
        text-align: center;
        color: #4B5563;
        border-radius: 50%;
        background-color: #F3F4F6;
        margin-left: 8px;
        padding:0 4px;
        min-width: 20px;
      }

      // 特定tab的颜色样式（适用于正常状态和骨架状态）
      &:nth-child(2) {
        .tab-count, .skeleton-count {
          background: #FEE2E2;
          color: #DC2626;
        }
      }
      &:nth-child(3) {
        .tab-count, .skeleton-count {
          background-color: #DCFCE7;
          color: #16A34A;
        }
      }

      &:hover,
      &.active {
        background: var(--fill-0);
      }

      // 骨架tab样式
      &.skeleton-tab {
        cursor: default;

        .tab-label {
          color: #9CA3AF;
        }

        .skeleton-count {
          background-color: #E5E7EB;
          color: #9CA3AF;
          animation: skeleton-loading 1.5s ease-in-out infinite;
        }

        &.active {
          background: var(--fill-0);

          .skeleton-count {
            background-color: #D1D5DB;
          }
        }

        // 骨架状态下保持特定颜色，但降低透明度
        &:nth-child(2) {
          .skeleton-count {
            background: rgba(254, 226, 226, 0.7);
            color: rgba(220, 38, 38, 0.7);
          }
        }
        &:nth-child(3) {
          .skeleton-count {
            background-color: rgba(220, 252, 231, 0.7);
            color: rgba(22, 163, 74, 0.7);
          }
        }
      }
    }
  }
  
  .review-items {  
    .skeleton-title-bar,
    .item-title-bar {
        display: flex;
        align-items: center; 
        padding: 12px 16px;
        background: #F5F5F5;
        border-bottom: 1px solid #E5E7EB; 
        .item-index {
          width: 6px;
          height: 16px;
          background-color: var(--main-6);
          border-radius: 2px;
          margin-right: 12px;
        }

        .item-title {
          font-size: var(--font-16); 
        }

        .item-count {
          color: #4B5563;
          border-radius: 50%;
          background-color: #E5E7EB;
          margin-left: 8px;
          padding:0 4px; 
          min-width: 20px; 
          text-align: center;
        }
      } 
  }
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

// 骨架屏样式
.skeleton-container {
  .skeleton-item-group {
    margin-bottom: 24px; 
    .skeleton-content {
      padding: 16px;

      .skeleton-review-item {
        padding: 16px 0;
        border-bottom: 1px solid #E5E7EB;

        &:last-child {
          border-bottom: none;
        }

        .skeleton-line {
          height: 16px;
          background: linear-gradient(90deg, #F3F4F6 25%, #E5E7EB 50%, #F3F4F6 75%);
          background-size: 200% 100%;
          border-radius: 4px;
          margin-bottom: 12px;
          animation: skeleton-loading 1.5s ease-in-out infinite;

          &:last-child {
            margin-bottom: 0;
          }

          &.skeleton-line-long {
            width: 85%;
          }

          &.skeleton-line-medium {
            width: 65%;
          }

          &.skeleton-line-short {
            width: 45%;
          }
        }
      }
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style> 