<template>
    <div class="cross-page-demo">
        <div class="demo-header">
            <h2>跨页内容解决方案演示</h2>
            <div class="demo-controls">
                <a-button type="primary" @click="loadDemoData">加载演示数据</a-button>
                <a-button @click="resetData">重置</a-button>
            </div>
        </div>

        <div class="demo-content">
            <div class="problem-section">
                <h3>问题描述</h3>
                <div class="problem-demo">
                    <div class="page-container problem">
                        <div class="page-header">第1页 - 问题：内容过长</div>
                        <div class="page-content">
                            <div class="content-item long-content">
                                这是一个很长的表格内容，在原始实现中会全部显示在第一页，导致页面过长...
                                <table class="demo-table">
                                    <tr><td>数据1</td><td>数据2</td><td>数据3</td></tr>
                                    <tr><td>数据4</td><td>数据5</td><td>数据6</td></tr>
                                    <tr><td>数据7</td><td>数据8</td><td>数据9</td></tr>
                                    <tr><td>数据10</td><td>数据11</td><td>数据12</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="page-container problem">
                        <div class="page-header">第2页 - 问题：空白页</div>
                        <div class="page-content empty">
                            <div class="empty-notice">空白页面</div>
                        </div>
                    </div>
                    <div class="page-container problem">
                        <div class="page-header">第3页 - 问题：空白页</div>
                        <div class="page-content empty">
                            <div class="empty-notice">空白页面</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="solution-section" v-if="showSolution">
                <h3>解决方案</h3>
                <div class="solution-demo">
                    <div class="page-container solution">
                        <div class="page-header">第1页 - 解决方案</div>
                        <div class="page-content">
                            <div class="content-item cross-page-item cross-page-first">
                                <div class="cross-page-header">
                                    <div class="cross-page-info">
                                        <span class="cross-page-badge">跨页内容</span>
                                        <span class="cross-page-progress">1 / 3</span>
                                    </div>
                                    <div class="cross-page-type">表格</div>
                                </div>
                                <div class="content">
                                    表格内容第一部分
                                    <table class="demo-table">
                                        <tr><td>数据1</td><td>数据2</td><td>数据3</td></tr>
                                        <tr><td>数据4</td><td>数据5</td><td>数据6</td></tr>
                                    </table>
                                    <span class="cross-page-indicator">（续下页）</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="page-container solution">
                        <div class="page-header">第2页 - 解决方案</div>
                        <div class="page-content">
                            <div class="content-item cross-page-item cross-page-middle">
                                <div class="cross-page-header">
                                    <div class="cross-page-info">
                                        <span class="cross-page-badge">跨页内容</span>
                                        <span class="cross-page-progress">2 / 3</span>
                                    </div>
                                    <div class="cross-page-type">表格</div>
                                </div>
                                <div class="content">
                                    <span class="cross-page-indicator">（接上页）</span>
                                    表格内容第二部分
                                    <table class="demo-table">
                                        <tr><td>数据7</td><td>数据8</td><td>数据9</td></tr>
                                        <tr><td>数据10</td><td>数据11</td><td>数据12</td></tr>
                                    </table>
                                    <span class="cross-page-indicator">（续下页）</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="page-container solution">
                        <div class="page-header">第3页 - 解决方案</div>
                        <div class="page-content">
                            <div class="content-item cross-page-item cross-page-last">
                                <div class="cross-page-header">
                                    <div class="cross-page-info">
                                        <span class="cross-page-badge">跨页内容</span>
                                        <span class="cross-page-progress">3 / 3</span>
                                    </div>
                                    <div class="cross-page-type">表格</div>
                                </div>
                                <div class="content">
                                    <span class="cross-page-indicator">（接上页）</span>
                                    表格内容第三部分
                                    <table class="demo-table">
                                        <tr><td>数据13</td><td>数据14</td><td>数据15</td></tr>
                                        <tr><td>数据16</td><td>数据17</td><td>数据18</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="features-section" v-if="showSolution">
                <h3>解决方案特性</h3>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <div class="feature-title">智能分割</div>
                        <div class="feature-desc">根据内容类型采用不同的分割策略</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎨</div>
                        <div class="feature-title">视觉标识</div>
                        <div class="feature-desc">颜色编码和进度指示器</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔗</div>
                        <div class="feature-title">连续性</div>
                        <div class="feature-desc">跨页标识保持内容连贯性</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <div class="feature-title">性能优化</div>
                        <div class="feature-desc">虚拟滚动支持大量页面</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

defineOptions({
    name: 'CrossPageDemo'
})

const showSolution = ref(false)

const loadDemoData = () => {
    showSolution.value = true
}

const resetData = () => {
    showSolution.value = false
}
</script>

<style lang="scss" scoped>
.cross-page-demo {
    padding: 24px;
    background: #f5f5f5;
    min-height: 100vh;

    .demo-header {
        background: white;
        padding: 24px;
        border-radius: 8px;
        margin-bottom: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h2 {
            margin: 0;
            color: #1890ff;
        }

        .demo-controls {
            display: flex;
            gap: 12px;
        }
    }

    .demo-content {
        .problem-section,
        .solution-section {
            background: white;
            padding: 24px;
            border-radius: 8px;
            margin-bottom: 24px;

            h3 {
                margin: 0 0 16px 0;
                color: #262626;
            }
        }

        .problem-demo,
        .solution-demo {
            display: flex;
            gap: 16px;
            overflow-x: auto;
            padding: 16px 0;
        }

        .page-container {
            min-width: 300px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            &.problem {
                border: 2px solid #ff4d4f;
            }

            &.solution {
                border: 2px solid #52c41a;
            }

            .page-header {
                padding: 12px 16px;
                background: #fafafa;
                border-bottom: 1px solid #e8e8e8;
                font-weight: 600;
                border-radius: 6px 6px 0 0;
            }

            .page-content {
                padding: 16px;
                min-height: 200px;

                &.empty {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #999;
                    font-style: italic;
                }

                .empty-notice {
                    padding: 40px;
                    background: #f8f9fa;
                    border: 2px dashed #dee2e6;
                    border-radius: 8px;
                }

                .long-content {
                    background: #fff2f0;
                    padding: 12px;
                    border-radius: 6px;
                    border-left: 4px solid #ff4d4f;
                }
            }
        }

        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 8px 0;

            td {
                border: 1px solid #d9d9d9;
                padding: 8px 12px;
                font-size: 14px;
                background: #fafafa;
            }
        }

        // 跨页内容样式
        .cross-page-item {
            border: 2px solid #e6f7ff;
            border-radius: 8px;
            background: #fafffe;
            padding: 12px;

            .cross-page-header {
                background: #e6f7ff;
                padding: 8px 12px;
                margin: -12px -12px 12px -12px;
                border-radius: 6px 6px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .cross-page-info {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .cross-page-badge {
                        background: #1890ff;
                        color: white;
                        padding: 2px 6px;
                        border-radius: 4px;
                        font-size: 12px;
                        font-weight: 500;
                    }

                    .cross-page-progress {
                        font-size: 12px;
                        color: #666;
                        background: white;
                        padding: 2px 6px;
                        border-radius: 4px;
                    }
                }

                .cross-page-type {
                    font-size: 12px;
                    color: #1890ff;
                    font-weight: 500;
                }
            }

            &.cross-page-first {
                border-color: #52c41a;

                .cross-page-header {
                    background: #f6ffed;

                    .cross-page-badge {
                        background: #52c41a;
                    }

                    .cross-page-type {
                        color: #52c41a;
                    }
                }
            }

            &.cross-page-middle {
                border-color: #fa8c16;

                .cross-page-header {
                    background: #fff7e6;

                    .cross-page-badge {
                        background: #fa8c16;
                    }

                    .cross-page-type {
                        color: #fa8c16;
                    }
                }
            }

            &.cross-page-last {
                border-color: #722ed1;

                .cross-page-header {
                    background: #f9f0ff;

                    .cross-page-badge {
                        background: #722ed1;
                    }

                    .cross-page-type {
                        color: #722ed1;
                    }
                }
            }
        }

        .cross-page-indicator {
            display: inline-block;
            background: #e6f7ff;
            color: #1890ff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin: 0 4px;
            border: 1px solid #91d5ff;
        }

        .features-section {
            .features-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 16px;
                margin-top: 16px;

                .feature-card {
                    background: #fafafa;
                    padding: 20px;
                    border-radius: 8px;
                    text-align: center;
                    border: 1px solid #e8e8e8;

                    .feature-icon {
                        font-size: 32px;
                        margin-bottom: 12px;
                    }

                    .feature-title {
                        font-size: 16px;
                        font-weight: 600;
                        color: #262626;
                        margin-bottom: 8px;
                    }

                    .feature-desc {
                        font-size: 14px;
                        color: #666;
                        line-height: 1.4;
                    }
                }
            }
        }
    }
}
</style>
