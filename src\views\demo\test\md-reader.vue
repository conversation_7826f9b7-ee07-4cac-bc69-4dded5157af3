<template>
    <div class="md-reader-container">
        <!-- 目录导航 -->
        <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
            <div class="sidebar-header">
                <h3>目录</h3>
                <a-button
                    type="text"
                    size="small"
                    @click="sidebarCollapsed = !sidebarCollapsed"
                >
                    <template #icon>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                        </svg>
                    </template>
                </a-button>
            </div>
            <div class="toc-container" v-if="!sidebarCollapsed">
                <div
                    v-for="item in tocItems"
                    :key="item.id"
                    class="toc-item"
                    :class="{
                        active: currentTocId === item.id,
                        [`level-${item.level}`]: true
                    }"
                    @click="jumpToSection(item)"
                >
                    {{ item.text }}
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <a-button-group>
                        <a-button @click="zoomOut" :disabled="zoom <= 50">
                            <template #icon>-</template>
                        </a-button>
                        <a-button disabled>{{ zoom }}%</a-button>
                        <a-button @click="zoomIn" :disabled="zoom >= 200">
                            <template #icon>+</template>
                        </a-button>
                    </a-button-group>

                    <a-select
                        v-model:value="currentPageId"
                        style="width: 120px; margin-left: 12px"
                        @change="jumpToPage"
                    >
                        <a-select-option
                            v-for="page in pageList"
                            :key="page.id"
                            :value="page.id"
                        >
                            第 {{ page.id }} 页
                        </a-select-option>
                    </a-select>
                </div>

                <div class="toolbar-right">
                    <a-button-group>
                        <a-button @click="testJumpPage(2)">跳转第2页</a-button>
                        <a-button @click="clearAllHighlights" title="清除所有高亮">
                            清除高亮
                        </a-button>
                        <a-button @click="debugCrossPageData" type="primary" ghost title="调试页面数据">
                            调试数据
                        </a-button>
                    </a-button-group>

                    <a-button
                        type="text"
                        @click="revisionPanelCollapsed = !revisionPanelCollapsed"
                        title="切换修订建议面板"
                    >
                        <template #icon>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21.11,3.89 20.11,3 19,3M19,19H5V5H19V19Z"/>
                            </svg>
                        </template>
                    </a-button>
                </div>
            </div>

            <!-- 文档内容区域 -->
            <div
                ref="contentContainer"
                class="content-container"
                @scroll="handleScroll"
            >
                <!-- 缩放包装器 -->
                <div
                    class="zoom-wrapper"
                    :style="{
                        transform: `scale(${zoom / 100})`,
                        transformOrigin: 'top left',
                        width: `${100 / (zoom / 100)}%`,
                        minWidth: '100%',
                    }"
                >
                <!-- 加载状态 -->
                <div v-if="loading" class="loading-container">
                    <a-spin size="large" tip="正在加载文档...">
                        <div class="loading-placeholder"></div>
                    </a-spin>
                </div>

                <!-- 错误状态 -->
                <div v-else-if="error" class="error-container">
                    <a-result
                        status="error"
                        title="加载失败"
                        :sub-title="error"
                    >
                        <template #extra>
                            <a-button type="primary" @click="loadDocumentData">
                                重新加载
                            </a-button>
                        </template>
                    </a-result>
                </div>

                <!-- 文档内容 -->
                <div
                    class="virtual-list"
                    :style="{ height: totalHeight + 'px' }"
                >
                    <div
                        class="visible-content"
                        :style="{ transform: `translateY(${offsetY}px)` }"
                    >
                        <div
                            v-for="page in visiblePages"
                            :key="page.id"
                            :ref="el => setPageRef(page.id, el)"
                            class="page-container"
                            :data-page-id="page.id"
                        >
                            <div class="page-header">
                                <span class="page-number">第 {{ page.id }} 页</span>
                            </div>

                            <div class="page-content" :class="{
                                'minimal-content': isPageNumberPage(page),
                                'long-content': hasLongContent(page),
                                'table-sections': hasTableSections(page)
                            }">
                                <!-- 页码页面提示 -->
                                <div v-if="isPageNumberPage(page)" class="page-number-notice">
                                    <div class="page-number-display">{{ page.items[0]?.text || '空白页' }}</div>
                                    <div class="page-type-hint">页码页面</div>
                                </div>

                                <!-- 长表格提示 -->
                                <div v-if="hasLongContent(page)" class="long-content-notice">
                                    <div class="long-content-indicator">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17M11,9H13V7H11V9Z"/>
                                        </svg>
                                        此页面包含长表格内容
                                    </div>
                                </div>

                                <!-- 表格段提示 -->
                                <div v-if="hasTableSections(page)" class="table-sections-notice">
                                    <div class="sections-indicator">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M3,3H21V5H3V3M3,7H21V9H3V7M3,11H21V13H3V11M3,15H21V17H3V15M3,19H21V21H3V19Z"/>
                                        </svg>
                                        表格已智能分段显示
                                    </div>
                                </div>

                                <div
                                    v-for="item in page.items"
                                    :key="item.paragraph_id"
                                    :ref="el => setParagraphRef(item.page_id, item.paragraph_id, el)"
                                    class="content-item"
                                    :class="[
                                        `type-${item.type}`,
                                        `level-${item.outline_level}`,
                                        {
                                            highlighted: isHighlighted(item),
                                            editing: editingItem?.paragraph_id === item.paragraph_id,
                                            'page-number-only': item.isPageNumberOnly,
                                            'long-table': item.isLongTable,
                                            'table-section': item.isTableSection,
                                            'table-section-first': item.tableSectionInfo?.isFirstSection
                                        }
                                    ]"
                                    :data-page-id="item.page_id"
                                    :data-paragraph-id="item.paragraph_id"
                                    @click="handleItemClick(item, $event)"
                                    @mouseup="handleTextSelection(item, $event)"
                                >
                                    <!-- 段落内容 -->
                                    <div
                                        v-if="item.type === 'paragraph'"
                                        class="paragraph-content"
                                    >
                                        <div
                                            v-if="editingItem?.paragraph_id === item.paragraph_id"
                                            class="edit-container"
                                        >
                                            <a-textarea
                                                v-model:value="editingText"
                                                :auto-size="{ minRows: 2 }"
                                                @blur="saveEdit"
                                                @keydown.ctrl.enter="saveEdit"
                                                @keydown.esc="cancelEdit"
                                            />
                                            <div class="edit-actions">
                                                <a-button size="small" @click="saveEdit" type="primary">保存</a-button>
                                                <a-button size="small" @click="cancelEdit">取消</a-button>
                                            </div>
                                        </div>
                                        <div
                                            v-else
                                            class="text-content"
                                            v-html="renderHighlightedText(item)"
                                        ></div>
                                    </div>

                                    <!-- 表格内容 -->
                                    <div
                                        v-else-if="item.type === 'table'"
                                        class="table-content"
                                        :class="{
                                            'long-table': item.isLongTable,
                                            'table-section': item.isTableSection,
                                            'table-main-part': item.isTableMainPart,
                                            'table-continuation': item.isTableContinuation
                                        }"
                                    >
                                        <!-- 长表格提示 -->
                                        <div v-if="item.isLongTable" class="table-info-banner long-table-banner">
                                            <div class="banner-content">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17M11,9H13V7H11V9Z"/>
                                                </svg>
                                                <span>长表格 ({{ Math.round((item.longTableInfo?.totalLength || 0) / 1000) }}K字符)</span>
                                            </div>
                                        </div>

                                        <!-- 表格段提示 -->
                                        <div v-if="item.isTableSection" class="table-info-banner section-banner">
                                            <div class="banner-content">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M3,3H21V5H3V3M3,7H21V9H3V7M3,11H21V13H3V11M3,15H21V17H3V15M3,19H21V21H3V19Z"/>
                                                </svg>
                                                <span>表格第{{ item.tableSectionInfo?.sectionIndex }}段 (共{{ item.tableSectionInfo?.totalSections }}段)</span>
                                            </div>
                                        </div>

                                        <!-- 表格主要部分提示 -->
                                        <div v-if="item.isTableMainPart" class="table-info-banner main-part-banner">
                                            <div class="banner-content">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M3,3H21V5H3V3M3,7H21V9H3V7M3,11H21V13H3V11M3,15H21V17H3V15M3,19H21V21H3V19Z"/>
                                                </svg>
                                                <span>表格第{{ item.tableContinuationInfo?.currentPart }}部分 (共{{ item.tableContinuationInfo?.totalParts }}部分) - 主页面</span>
                                            </div>
                                        </div>

                                        <!-- 表格续页提示 -->
                                        <div v-if="item.isTableContinuation" class="table-info-banner continuation-banner">
                                            <div class="banner-content">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M3,3H21V5H3V3M3,7H21V9H3V7M3,11H21V13H3V11M3,15H21V17H3V15M3,19H21V21H3V19Z"/>
                                                </svg>
                                                <span>表格第{{ item.tableContinuationInfo?.currentPart }}部分 (共{{ item.tableContinuationInfo?.totalParts }}部分) - 续页</span>
                                                <a-button
                                                    size="small"
                                                    type="link"
                                                    @click="goToPage(item.tableContinuationInfo?.mainPageId)"
                                                    style="margin-left: 8px; padding: 0; height: auto;"
                                                >
                                                    返回主页面
                                                </a-button>
                                            </div>
                                        </div>

                                        <div v-html="item.text"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </div> <!-- 缩放包装器结束 -->
            </div>
        </div>

        <!-- 修订建议面板 -->
        <div class="revision-panel" :class="{ collapsed: revisionPanelCollapsed }">
            <div class="revision-header">
                <h3>修订建议</h3>
                <div class="header-actions">
                    <a-button
                        type="text"
                        size="small"
                        @click="clearAllHighlights"
                        title="清除高亮"
                    >
                        <template #icon>
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                            </svg>
                        </template>
                    </a-button>
                    <a-button
                        type="text"
                        size="small"
                        @click="revisionPanelCollapsed = !revisionPanelCollapsed"
                        title="收起面板"
                    >
                        <template #icon>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"/>
                            </svg>
                        </template>
                    </a-button>
                </div>
            </div>

            <div class="revision-content" v-if="!revisionPanelCollapsed">
                <div class="revision-stats">
                    <a-statistic-countdown
                        title="待处理建议"
                        :value="revisionSuggestions.length"
                        format="D"
                    />
                </div>

                <div class="revision-filters">
                    <a-radio-group v-model:value="selectedRevisionType" size="small">
                        <a-radio-button value="all">全部</a-radio-button>
                        <a-radio-button value="add">新增</a-radio-button>
                        <a-radio-button value="delete">删除</a-radio-button>
                        <a-radio-button value="modify">修改</a-radio-button>
                    </a-radio-group>
                </div>

                <div class="revision-list">
                    <div
                        v-for="suggestion in filteredRevisionSuggestions"
                        :key="suggestion.id"
                        class="revision-item"
                        :class="[
                            `type-${suggestion.type}`,
                            { active: activeSuggestionId === suggestion.id }
                        ]"
                        @click="jumpToSuggestion(suggestion)"
                    >
                        <div class="revision-item-header">
                            <a-tag
                                :color="getRevisionTypeColor(suggestion.type)"
                                size="small"
                            >
                                {{ getRevisionTypeText(suggestion.type) }}
                            </a-tag>
                            <span class="page-info">第{{ suggestion.pageId }}页</span>
                        </div>

                        <div class="revision-item-content">
                            <div v-if="suggestion.type === 'delete'" class="original-text">
                                <span class="label">删除内容：</span>
                                <span class="text deleted">{{ suggestion.originalText }}</span>
                            </div>

                            <div v-else-if="suggestion.type === 'add'" class="suggested-text">
                                <span class="label">新增内容：</span>
                                <span class="text added">{{ suggestion.suggestedText }}</span>
                            </div>

                            <div v-else-if="suggestion.type === 'modify'">
                                <div class="original-text">
                                    <span class="label">原文：</span>
                                    <span class="text deleted">{{ suggestion.originalText }}</span>
                                </div>
                                <div class="suggested-text">
                                    <span class="label">建议：</span>
                                    <span class="text added">{{ suggestion.suggestedText }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="revision-item-actions">
                            <a-button-group size="small">
                                <a-button
                                    type="primary"
                                    @click.stop="acceptSuggestion(suggestion)"
                                >
                                    接受
                                </a-button>
                                <a-button @click.stop="rejectSuggestion(suggestion)">
                                    拒绝
                                </a-button>
                            </a-button-group>
                        </div>

                        <div class="revision-item-reason" v-if="suggestion.reason">
                            <span class="label">建议理由：</span>
                            <span class="reason">{{ suggestion.reason }}</span>
                        </div>
                    </div>
                </div>

                <!-- 建议输入区域 -->
                <div class="suggestion-input">
                    <h4>添加建议</h4>
                    <a-textarea
                        v-model:value="newSuggestionText"
                        placeholder="请输入修订建议..."
                        :rows="3"
                    />
                    <div class="input-actions">
                        <a-button
                            type="primary"
                            size="small"
                            @click="addSuggestion"
                            :disabled="!newSuggestionText.trim()"
                        >
                            添加建议
                        </a-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang='ts'>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { taskMarkDownDetail } from '@/api/examine'
defineOptions({
    name: 'MdReaderIndex'
})

// 类型定义
interface DocumentItem {
    page_id: number
    paragraph_id: number
    outline_level: string
    text: string
    type: 'paragraph' | 'table'
    position: string
    tags: string[]
    sub_type?: string
    cells?: TableCell[]
    split_section_page_ids?: number[]
    split_section_positions?: number[][]
    image_url?: string
    isPageNumberOnly?: boolean // 标记是否只是页码
    isPageFooter?: boolean // 标记是否为页脚页码
    originalPageId?: number // 原始页面ID（用于合并的页码）
    isLongTable?: boolean // 标记是否为长表格
    isTableSection?: boolean // 标记是否为表格段
    isTableMainPart?: boolean // 标记是否为表格主要部分
    isTableContinuation?: boolean // 标记是否为表格续页
    longTableInfo?: {
        totalLength: number
        estimatedHeight: number
    }
    tableSectionInfo?: {
        sectionIndex: number
        totalSections: number
        isFirstSection: boolean
    }
    tableContinuationInfo?: {
        totalParts: number
        currentPart: number
        totalRows: number
        mainPageId: number
        originalPageId?: number
    }
}

interface TableCell {
    col: number
    col_span: number
    position: number[]
    row: number
    row_span: number
    text: string
    type: 'cell'
}

interface PageData {
    id: number
    items: DocumentItem[]
    height: number
}

interface TocItem {
    id: string
    text: string
    level: number
    pageId: number
    paragraphId: number
}

interface HighlightData {
    id: string
    pageId: number
    paragraphId: number
    startOffset: number
    endOffset: number
    color: string
    text: string
    timestamp: number
}

interface RevisionSuggestion {
    id: string
    type: 'add' | 'delete' | 'modify'
    pageId: number
    paragraphId: number
    position: {
        x1: number
        y1: number
        x2: number
        y2: number
    }
    originalText?: string
    suggestedText?: string
    reason?: string
    timestamp: number
    status: 'pending' | 'accepted' | 'rejected'
}

// 响应式数据
const documentData = ref<DocumentItem[]>([])
const loading = ref(true)
const error = ref('')

// 布局相关
const sidebarCollapsed = ref(false)
const contentContainer = ref<HTMLElement>()
const pageRefs = ref<Map<number, HTMLElement>>(new Map())
const paragraphRefs = ref<Map<string, HTMLElement>>(new Map())

// 虚拟滚动相关
const scrollTop = ref(0)
const containerHeight = ref(800)
const pageHeight = ref(1000) // 估算的页面高度
const visibleRange = ref({ start: 0, end: 0 })
const offsetY = ref(0)
const pageHeights = ref<Map<number, number>>(new Map()) // 实际页面高度缓存

// 导航相关
const currentPageId = ref(1)
const currentTocId = ref('')
const tocItems = ref<TocItem[]>([])

// 缩放相关
const zoom = ref(100)

// 高亮相关
const highlightMode = ref(false)
const highlights = ref<Map<string, HighlightData[]>>(new Map())
const selectedText = ref('')
const selectedRange = ref<Range | null>(null)
const showColorPicker = ref(false)
const colorPickerPosition = ref({ top: '0px', left: '0px' })

// 编辑相关
const editMode = ref(false)
const editingItem = ref<DocumentItem | null>(null)
const editingText = ref('')

// 修订建议相关
const revisionPanelCollapsed = ref(false)
const revisionSuggestions = ref<RevisionSuggestion[]>([])
const selectedRevisionType = ref<'all' | 'add' | 'delete' | 'modify'>('all')
const activeSuggestionId = ref<string>('')
const newSuggestionText = ref('')



// 计算属性
const pageList = computed(() => {
    const pages: { id: number }[] = []
    const pageIds = new Set<number>()

    documentData.value.forEach(item => {
        if (!pageIds.has(item.page_id)) {
            pageIds.add(item.page_id)
            pages.push({ id: item.page_id })
        }
    })

    return pages.sort((a, b) => a.id - b.id)
})

const pagesData = computed(() => {
    const pages: PageData[] = []
    const pageMap = new Map<number, DocumentItem[]>()

    // 按页面分组
    documentData.value.forEach(item => {
        if (!pageMap.has(item.page_id)) {
            pageMap.set(item.page_id, [])
        }
        pageMap.get(item.page_id)!.push(item)
    })

    // 智能重分配内容到页码页
    const redistributedPages = redistributeContentToPages(pageMap)

    // 转换为页面数据
    redistributedPages.forEach((items, pageId) => {
        const pageAnalysis = analyzePageContent(items, pageId)

        pages.push({
            id: pageId,
            items: pageAnalysis.processedItems,
            height: pageAnalysis.pageHeight
        })
    })

    return pages.sort((a, b) => a.id - b.id)
})

// 智能重分配内容到页码页
const redistributeContentToPages = (pageMap: Map<number, DocumentItem[]>): Map<number, DocumentItem[]> => {
    const redistributedPages = new Map<number, DocumentItem[]>()
    const sortedPageIds = Array.from(pageMap.keys()).sort((a, b) => a - b)

    // 第一步：识别页码页和内容页
    const pageNumberPages: number[] = []
    const contentPages: number[] = []

    sortedPageIds.forEach(pageId => {
        const items = pageMap.get(pageId)!
        const isPageNumberOnly = items.length === 1 &&
            /^\d+$/.test(items[0].text.trim()) &&
            items[0].text.trim().length <= 3

        if (isPageNumberOnly) {
            pageNumberPages.push(pageId)
        } else {
            contentPages.push(pageId)
        }
    })

    // 第二步：处理内容页，拆分长内容
    contentPages.forEach(pageId => {
        const items = pageMap.get(pageId)!
        const longTableItems = items.filter(item =>
            item.type === 'table' && item.text.length > 8000
        )
        const normalItems = items.filter(item =>
            !(item.type === 'table' && item.text.length > 8000)
        )

        if (longTableItems.length > 0) {
            // 有长表格，需要拆分
            const splitResult = splitLongTableToPages(longTableItems[0], pageId, pageNumberPages)

            // 主页面保留正常内容和表格第一部分
            redistributedPages.set(pageId, [
                ...normalItems,
                splitResult.mainTablePart
            ])

            // 将表格续页分配到页码页
            splitResult.continuationParts.forEach((tablePart, index) => {
                const targetPageId = splitResult.targetPageIds[index]
                if (targetPageId) {
                    redistributedPages.set(targetPageId, [tablePart])
                }
            })
        } else {
            // 没有长表格，直接保留
            redistributedPages.set(pageId, items)
        }
    })

    // 第三步：处理剩余的页码页（没有被分配内容的）
    pageNumberPages.forEach(pageId => {
        if (!redistributedPages.has(pageId)) {
            const originalPageNumber = pageMap.get(pageId)![0]
            redistributedPages.set(pageId, [{
                ...originalPageNumber,
                isPageNumberOnly: true
            }])
        }
    })

    return redistributedPages
}

// 拆分长表格到多个页面
const splitLongTableToPages = (tableItem: DocumentItem, mainPageId: number, availablePageIds: number[]) => {
    const tableHtml = tableItem.text
    const maxRowsPerPage = 15 // 每页最多显示的行数

    // 解析表格行
    const rowMatches = tableHtml.match(/<tr[^>]*>.*?<\/tr>/gs)
    if (!rowMatches || rowMatches.length <= maxRowsPerPage) {
        // 表格不需要拆分
        return {
            mainTablePart: tableItem,
            continuationParts: [],
            targetPageIds: []
        }
    }

    const headerRow = rowMatches[0] // 表头
    const dataRows = rowMatches.slice(1) // 数据行
    const continuationParts: DocumentItem[] = []
    const targetPageIds: number[] = []

    // 主页面显示前N行
    const mainPageRows = dataRows.slice(0, maxRowsPerPage)
    const mainTableHtml = `<table border="1">${headerRow}${mainPageRows.join('')}</table>`

    const mainTablePart: DocumentItem = {
        ...tableItem,
        text: mainTableHtml,
        isTableMainPart: true,
        tableContinuationInfo: {
            totalParts: Math.ceil(dataRows.length / maxRowsPerPage),
            currentPart: 1,
            totalRows: dataRows.length,
            mainPageId
        }
    }

    // 续页内容
    const remainingRows = dataRows.slice(maxRowsPerPage)
    let availablePageIndex = 0

    for (let i = 0; i < remainingRows.length; i += maxRowsPerPage) {
        if (availablePageIndex >= availablePageIds.length) break

        const pageRows = remainingRows.slice(i, i + maxRowsPerPage)
        const pageTableHtml = `<table border="1">${headerRow}${pageRows.join('')}</table>`

        const partIndex = Math.floor(i / maxRowsPerPage) + 2 // +2 因为主页面是第1部分
        const targetPageId = availablePageIds[availablePageIndex]

        const continuationPart: DocumentItem = {
            ...tableItem,
            paragraph_id: tableItem.paragraph_id + partIndex * 1000, // 确保ID唯一
            text: pageTableHtml,
            isTableContinuation: true,
            tableContinuationInfo: {
                totalParts: Math.ceil(dataRows.length / maxRowsPerPage),
                currentPart: partIndex,
                totalRows: dataRows.length,
                mainPageId,
                originalPageId: targetPageId
            }
        }

        continuationParts.push(continuationPart)
        targetPageIds.push(targetPageId)
        availablePageIndex++
    }

    return {
        mainTablePart,
        continuationParts,
        targetPageIds
    }
}



// 分析页面内容并进行优化处理
const analyzePageContent = (items: DocumentItem[], pageId: number) => {
    // 检查是否为纯页码页
    const isPageNumberOnly = items.length === 1 &&
        /^\d+$/.test(items[0].text.trim()) &&
        items[0].text.trim().length <= 3

    // 检查是否有超长表格
    const hasLongTable = items.some(item =>
        item.type === 'table' && item.text.length > 8000
    )

    // 检查总内容长度
    const totalContentLength = items.reduce((sum, item) => sum + item.text.length, 0)

    let processedItems = [...items]
    let currentPageHeight = pageHeight.value

    if (isPageNumberOnly) {
        // 页码页：标记并使用最小高度
        processedItems[0] = {
            ...processedItems[0],
            isPageNumberOnly: true
        }
        currentPageHeight = 100
    } else if (hasLongTable) {
        // 长表格页：添加提示信息
        processedItems = items.map(item => {
            if (item.type === 'table' && item.text.length > 8000) {
                return {
                    ...item,
                    isLongTable: true,
                    longTableInfo: {
                        totalLength: item.text.length,
                        estimatedHeight: Math.ceil(item.text.length / 100) * 20
                    }
                }
            }
            return item
        })
        // 根据内容长度动态调整高度
        currentPageHeight = Math.max(800, Math.min(1500, totalContentLength / 8))
    } else if (totalContentLength > 3000) {
        // 内容较多的页面
        currentPageHeight = Math.max(600, Math.min(1200, totalContentLength / 10))
    }

    return {
        processedItems: processedItems.sort((a, b) => a.paragraph_id - b.paragraph_id),
        pageHeight: currentPageHeight,
        isPageNumberOnly,
        hasLongTable,
        totalContentLength
    }
}

const totalHeight = computed(() => {
    let total = 0
    pagesData.value.forEach((page, index) => {
        const cachedHeight = pageHeights.value.get(page.id)
        total += cachedHeight || pageHeight.value
        // 除了最后一页，每页都加上间距
        if (index < pagesData.value.length - 1) {
            total += 32
        }
    })
    return total
})

const visiblePages = computed(() => {
    const { start, end } = visibleRange.value
    const pages = pagesData.value.slice(start, end + 1)

    // 调试信息
    if (pages.length === 0 && pagesData.value.length > 0) {
        console.warn('虚拟滚动渲染失败:', {
            totalPages: pagesData.value.length,
            visibleRange: visibleRange.value,
            scrollTop: scrollTop.value,
            totalHeight: totalHeight.value
        })

        // 紧急修复：如果没有可见页面，至少显示第一页
        return pagesData.value.slice(0, 1)
    }

    return pages
})

const filteredRevisionSuggestions = computed(() => {
    if (selectedRevisionType.value === 'all') {
        return revisionSuggestions.value.filter(s => s.status === 'pending')
    }
    return revisionSuggestions.value.filter(s =>
        s.type === selectedRevisionType.value && s.status === 'pending'
    )
})

// 生命周期
onMounted(async () => {
    await loadDocumentData()
    initializeContainer()
    generateToc()
    loadHighlights()
    loadRevisionSuggestions()

    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeydown)
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
    window.removeEventListener('resize', handleResize)
})

// 数据加载
const loadDocumentData = async () => {
    loading.value = true
    try {
        // 先尝试加载真实数据
        const {data, err} = await taskMarkDownDetail({taskId:'1955823962696978434'})
        if (!err && data.markDownDetail) {
            const rawData = JSON.parse(data.markDownDetail)
            documentData.value = rawData // 保持原始数据结构
        } else {
            // 如果API失败，加载本地测试数据
            const response = await fetch('/src/views/demo/test/1.json')
            const rawData = await response.json()
            documentData.value = rawData // 保持原始数据结构
        }
    } catch (e) {
        console.error('加载数据失败:', e)
        error.value = '加载数据失败'
    } finally {
        loading.value = false
    }
}



// 容器初始化
const initializeContainer = () => {
    if (contentContainer.value) {
        containerHeight.value = contentContainer.value.clientHeight
        updateVisibleRange()
    }
}



// 生成目录 - 只识别章节级别
const generateToc = () => {
    const toc: TocItem[] = []

    documentData.value.forEach(item => {
        const level = parseInt(item.outline_level)
        // 只识别 0 和 1 级别的标题作为章节
        if ((level === 0 || level === 1) && item.type === 'paragraph') {
            toc.push({
                id: `${item.page_id}-${item.paragraph_id}`,
                text: item.text,
                level,
                pageId: item.page_id,
                paragraphId: item.paragraph_id
            })
        }
    })

    tocItems.value = toc
}

// 虚拟滚动相关方法
const updateVisibleRange = () => {
    if (pagesData.value.length === 0) return

    const totalPages = pagesData.value.length
    let accumulatedHeight = 0
    let startIndex = 0
    let endIndex = totalPages - 1

    // 找到开始显示的页面索引
    for (let i = 0; i < totalPages; i++) {
        const page = pagesData.value[i]
        const height = pageHeights.value.get(page.id) || pageHeight.value

        if (scrollTop.value < accumulatedHeight + height) {
            startIndex = Math.max(0, i - 2) // 提前加载两页
            break
        }
        accumulatedHeight += height + 32 // 加上页面间距
    }

    // 如果滚动到了最底部，确保显示最后几页
    if (scrollTop.value + containerHeight.value >= totalHeight.value - 100) {
        startIndex = Math.max(0, totalPages - 10) // 显示最后10页
        endIndex = totalPages - 1
    } else {
        // 找到结束显示的页面索引
        let visibleHeight = 0
        for (let i = startIndex; i < totalPages; i++) {
            const page = pagesData.value[i]
            const height = pageHeights.value.get(page.id) || pageHeight.value
            visibleHeight += height + 32

            if (visibleHeight >= containerHeight.value + 400) { // 多加载一些
                endIndex = Math.min(i + 2, totalPages - 1) // 多加载两页
                break
            }
        }
    }

    // 确保至少显示一些页面
    if (endIndex - startIndex < 5) {
        endIndex = Math.min(startIndex + 5, totalPages - 1)
    }

    visibleRange.value = { start: startIndex, end: endIndex }

    // 计算偏移量
    let offsetHeight = 0
    for (let i = 0; i < startIndex; i++) {
        const page = pagesData.value[i]
        const height = pageHeights.value.get(page.id) || pageHeight.value
        offsetHeight += height + 32
    }
    offsetY.value = offsetHeight
}

const handleScroll = () => {
    if (!contentContainer.value) return

    scrollTop.value = contentContainer.value.scrollTop
    updateVisibleRange()
    updateCurrentPage()
}

// 强制刷新虚拟滚动
const forceUpdateVisibleRange = () => {
    if (!contentContainer.value) return

    scrollTop.value = contentContainer.value.scrollTop
    containerHeight.value = contentContainer.value.clientHeight
    updateVisibleRange()
}

const updateCurrentPage = () => {
    if (pagesData.value.length === 0) return

    let accumulatedHeight = 0
    let currentPage = pagesData.value[0].id

    // 根据实际滚动位置计算当前页面
    for (let i = 0; i < pagesData.value.length; i++) {
        const page = pagesData.value[i]
        const height = pageHeights.value.get(page.id) || pageHeight.value

        // 如果滚动位置在当前页面范围内
        if (scrollTop.value < accumulatedHeight + height / 2) {
            currentPage = page.id
            break
        }

        accumulatedHeight += height + 32 // 页面间距
    }

    if (currentPage !== currentPageId.value) {
        currentPageId.value = currentPage
    }

    // 更新当前目录项
    updateCurrentToc()
}

const updateCurrentToc = () => {
    // 根据当前滚动位置确定当前的目录项
    let accumulatedHeight = 0
    let currentPageIndex = -1

    // 使用实际高度计算当前页面
    for (let i = 0; i < pagesData.value.length; i++) {
        const page = pagesData.value[i]
        const pageHeight = pageHeights.value.get(page.id) || 1000

        if (scrollTop.value >= accumulatedHeight && scrollTop.value < accumulatedHeight + pageHeight) {
            currentPageIndex = i
            break
        }

        accumulatedHeight += pageHeight
    }

    if (currentPageIndex >= 0) {
        const currentPage = pagesData.value[currentPageIndex]

        // 找到当前页面中最接近的目录项
        const pageTocItems = tocItems.value.filter(item => item.pageId === currentPage.id)
        if (pageTocItems.length > 0) {
            // 简单选择第一个，实际项目中可以根据段落位置进一步精确定位
            currentTocId.value = pageTocItems[0].id
        }
    }
}

// 页面引用管理
const setPageRef = (pageId: number, el: any) => {
    if (el) {
        pageRefs.value.set(pageId, el)

        // 使用 ResizeObserver 监听页面高度变化
        const resizeObserver = new ResizeObserver(entries => {
            for (const entry of entries) {
                const height = entry.contentRect.height
                pageHeights.value.set(pageId, height)
            }
        })

        resizeObserver.observe(el)
    }
}

const setParagraphRef = (pageId: number, paragraphId: number, el: any) => {
    if (el) {
        paragraphRefs.value.set(`${pageId}-${paragraphId}`, el)
    }
}

// 导航方法
const jumpToPage = (pageId: number) => {
    const pageIndex = pagesData.value.findIndex(page => page.id === pageId)
    if (pageIndex !== -1 && contentContainer.value) {
        let targetScrollTop = 0

        // 计算到目标页面的累积高度
        for (let i = 0; i < pageIndex; i++) {
            const page = pagesData.value[i]
            const height = pageHeights.value.get(page.id) || pageHeight.value
            targetScrollTop += height + 32 // 包含页面间距
        }

        // 如果是最后几页，确保不会超出总高度
        const maxScrollTop = Math.max(0, totalHeight.value - containerHeight.value)
        targetScrollTop = Math.min(targetScrollTop, maxScrollTop)

        // 先更新可见范围，确保目标页面会被渲染
        const totalPages = pagesData.value.length
        if (pageIndex >= totalPages - 10) {
            // 如果是最后10页，强制显示最后10页
            visibleRange.value = {
                start: Math.max(0, totalPages - 10),
                end: totalPages - 1
            }
        }

        contentContainer.value.scrollTo({
            top: targetScrollTop,
            behavior: 'smooth'
        })

        // 滚动完成后强制刷新虚拟滚动
        setTimeout(() => {
            forceUpdateVisibleRange()
        }, 500)

        // 更新当前页面ID
        currentPageId.value = pageId
    }
}

const jumpToSection = (tocItem: TocItem) => {
    // 先跳转到对应页面
    jumpToPage(tocItem.pageId)

    // 延迟一下等页面渲染完成后再定位到具体段落
    setTimeout(() => {
        const elementKey = `${tocItem.pageId}-${tocItem.paragraphId}`
        const element = paragraphRefs.value.get(elementKey)

        if (element && contentContainer.value) {
            // 计算到目标页面的累积高度
            let targetScrollTop = 0
            const pageIndex = pagesData.value.findIndex(page => page.id === tocItem.pageId)

            for (let i = 0; i < pageIndex; i++) {
                const page = pagesData.value[i]
                const height = pageHeights.value.get(page.id) || pageHeight.value
                targetScrollTop += height + 32 // 页面间距
            }

            // 加上段落在页面内的偏移
            const elementTop = element.offsetTop
            targetScrollTop += elementTop - 100 // 留出一些顶部空间

            contentContainer.value.scrollTo({
                top: targetScrollTop,
                behavior: 'smooth'
            })

            // 更新当前目录项
            currentTocId.value = tocItem.id
        }
    }, 300)
}

// 缩放方法
const zoomIn = () => {
    if (zoom.value < 200) {
        zoom.value += 10
    }
}

const zoomOut = () => {
    if (zoom.value > 50) {
        zoom.value -= 10
    }
}

// 模式切换
const toggleHighlightMode = () => {
    highlightMode.value = !highlightMode.value
    if (highlightMode.value) {
        editMode.value = false
    }
}

const toggleEditMode = () => {
    editMode.value = !editMode.value
    if (editMode.value) {
        highlightMode.value = false
    }
}

// 内容交互方法
const handleItemClick = (item: DocumentItem, _event: MouseEvent) => {
    if (editMode.value && item.type === 'paragraph') {
        startEdit(item)
    }
}

const handleTextSelection = (_item: DocumentItem, event: MouseEvent) => {
    if (!highlightMode.value) return

    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
        selectedText.value = selection.toString()
        selectedRange.value = selection.getRangeAt(0)

        // 显示颜色选择器
        showColorPicker.value = true
        colorPickerPosition.value = {
            top: event.clientY + 10 + 'px',
            left: event.clientX + 'px'
        }
    }
}

// 高亮相关方法
const isHighlighted = (item: DocumentItem): boolean => {
    const key = `${item.page_id}-${item.paragraph_id}`
    return highlights.value.has(key) && highlights.value.get(key)!.length > 0
}

const renderHighlightedText = (item: DocumentItem): string => {
    const key = `${item.page_id}-${item.paragraph_id}`
    const itemHighlights = highlights.value.get(key) || []

    if (itemHighlights.length === 0) {
        return item.text
    }

    // 简单的高亮渲染，实际项目中需要更复杂的逻辑
    let text = item.text
    itemHighlights.forEach(highlight => {
        const highlightedText = `<mark style="background-color: ${highlight.color}">${highlight.text}</mark>`
        text = text.replace(highlight.text, highlightedText)
    })

    return text
}





const loadHighlights = () => {
    // 从本地存储或API加载高亮数据
    const saved = localStorage.getItem('document-highlights')
    if (saved) {
        try {
            const data = JSON.parse(saved)
            highlights.value = new Map(data)
        } catch (e) {
            console.error('加载高亮数据失败:', e)
        }
    }
}

// 编辑相关方法
const startEdit = (item: DocumentItem) => {
    editingItem.value = item
    editingText.value = item.text
}

const saveEdit = async () => {
    if (!editingItem.value || editingText.value.trim() === '') return

    try {
        // 保存到本地存储作为备份
        const backupKey = `edit-backup-${editingItem.value.page_id}-${editingItem.value.paragraph_id}`
        localStorage.setItem(backupKey, JSON.stringify({
            originalText: editingItem.value.text,
            editedText: editingText.value,
            timestamp: Date.now()
        }))

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))

        // 更新本地数据
        const index = documentData.value.findIndex(item =>
            item.page_id === editingItem.value!.page_id &&
            item.paragraph_id === editingItem.value!.paragraph_id
        )

        if (index !== -1) {
            documentData.value[index].text = editingText.value
        }

        // 清除备份
        localStorage.removeItem(backupKey)

        cancelEdit()

        // 显示成功提示
        console.log('保存成功')

    } catch (error) {
        console.error('保存编辑失败:', error)
        // 这里可以显示错误提示，让用户选择重试或恢复
    }
}

const cancelEdit = () => {
    editingItem.value = null
    editingText.value = ''
}



// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
    // Ctrl + H: 切换高亮模式
    if (event.ctrlKey && event.key === 'h') {
        event.preventDefault()
        toggleHighlightMode()
    }

    // Ctrl + E: 切换编辑模式
    if (event.ctrlKey && event.key === 'e') {
        event.preventDefault()
        toggleEditMode()
    }

    // ESC: 退出当前模式
    if (event.key === 'Escape') {
        if (editingItem.value) {
            cancelEdit()
        }
        if (showColorPicker.value) {
            showColorPicker.value = false
        }
    }
}

const handleResize = () => {
    if (contentContainer.value) {
        containerHeight.value = contentContainer.value.clientHeight
        updateVisibleRange()
    }
}

// 测试方法（保留原有的测试功能）
const testJumpPage = (page: number) => {
    jumpToPage(page)
}



// 修订建议相关方法
const getRevisionTypeColor = (type: 'add' | 'delete' | 'modify'): string => {
    const colors = {
        add: 'green',
        delete: 'red',
        modify: 'orange'
    }
    return colors[type]
}

const getRevisionTypeText = (type: 'add' | 'delete' | 'modify'): string => {
    const texts = {
        add: '新增',
        delete: '删除',
        modify: '修改'
    }
    return texts[type]
}

const jumpToSuggestion = (suggestion: RevisionSuggestion) => {
    activeSuggestionId.value = suggestion.id

    // 跳转到对应页面
    jumpToPage(suggestion.pageId)

    // 延迟高亮，等待页面渲染完成
    setTimeout(() => {
        highlightSuggestionArea(suggestion)
    }, 500)
}

const highlightSuggestionArea = (suggestion: RevisionSuggestion) => {
    // 找到对应的段落元素
    const elementKey = `${suggestion.pageId}-${suggestion.paragraphId}`
    const element = paragraphRefs.value.get(elementKey)

    if (element) {
        // 移除之前的高亮
        document.querySelectorAll('.suggestion-highlight').forEach(el => {
            el.classList.remove('suggestion-highlight')
        })

        // 添加高亮效果 - 持续显示，不自动消失
        element.classList.add('suggestion-highlight')

        // 滚动到元素位置
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        })
    }
}

const acceptSuggestion = (suggestion: RevisionSuggestion) => {
    // 更新建议状态
    suggestion.status = 'accepted'

    // 应用修改到文档数据
    const itemIndex = documentData.value.findIndex(item =>
        item.page_id === suggestion.pageId &&
        item.paragraph_id === suggestion.paragraphId
    )

    if (itemIndex !== -1) {
        const item = documentData.value[itemIndex]

        switch (suggestion.type) {
            case 'delete':
                // 删除段落或标记为删除
                item.text = ''
                break
            case 'add':
                // 添加新内容
                if (suggestion.suggestedText) {
                    item.text += suggestion.suggestedText
                }
                break
            case 'modify':
                // 修改内容
                if (suggestion.suggestedText) {
                    item.text = suggestion.suggestedText
                }
                break
        }
    }

    // 清除激活状态和高亮
    if (activeSuggestionId.value === suggestion.id) {
        activeSuggestionId.value = ''
        // 清除对应的高亮
        const elementKey = `${suggestion.pageId}-${suggestion.paragraphId}`
        const element = paragraphRefs.value.get(elementKey)
        if (element) {
            element.classList.remove('suggestion-highlight')
        }
    }

    console.log('接受建议:', suggestion)
}

const rejectSuggestion = (suggestion: RevisionSuggestion) => {
    // 更新建议状态
    suggestion.status = 'rejected'

    // 清除激活状态和高亮
    if (activeSuggestionId.value === suggestion.id) {
        activeSuggestionId.value = ''
        // 清除对应的高亮
        const elementKey = `${suggestion.pageId}-${suggestion.paragraphId}`
        const element = paragraphRefs.value.get(elementKey)
        if (element) {
            element.classList.remove('suggestion-highlight')
        }
    }

    console.log('拒绝建议:', suggestion)
}

const addSuggestion = () => {
    if (!newSuggestionText.value.trim()) return

    // 创建新建议（这里简化处理，实际应该让用户选择位置）
    const newSuggestion: RevisionSuggestion = {
        id: Date.now().toString(),
        type: 'modify',
        pageId: currentPageId.value,
        paragraphId: 0,
        position: { x1: 0, y1: 0, x2: 100, y2: 20 },
        suggestedText: newSuggestionText.value,
        reason: '用户添加的建议',
        timestamp: Date.now(),
        status: 'pending'
    }

    revisionSuggestions.value.push(newSuggestion)
    newSuggestionText.value = ''

    console.log('添加建议:', newSuggestion)
}

// 加载修订建议数据
const loadRevisionSuggestions = () => {
    // 模拟修订建议数据
    const mockSuggestions: RevisionSuggestion[] = [
        {
            id: '1',
            type: 'modify',
            pageId: 1,
            paragraphId: 2,
            position: { x1: 467, y1: 1154, x2: 1059, y2: 1203 },
            originalText: '项目编号：SZDL2025001316',
            suggestedText: '项目编号：SZDL2025001316（已更新）',
            reason: '项目编号需要更新为最新版本',
            timestamp: Date.now() - 3600000,
            status: 'pending'
        },
        {
            id: '2',
            type: 'delete',
            pageId: 2,
            paragraphId: 1,
            position: { x1: 224, y1: 437, x2: 1578, y2: 1059 },
            originalText: '资格性审查表中的第1项内容',
            reason: '此项内容重复，建议删除',
            timestamp: Date.now() - 1800000,
            status: 'pending'
        },
        {
            id: '3',
            type: 'add',
            pageId: 5,
            paragraphId: 3,
            position: { x1: 323, y1: 816, x2: 632, y2: 860 },
            suggestedText: '注意：价格分计算方法仅适用于本次招标项目。',
            reason: '需要添加价格计算方法的适用范围说明',
            timestamp: Date.now() - 900000,
            status: 'pending'
        }
    ]

    revisionSuggestions.value = mockSuggestions
}

// 页面类型判断辅助方法
const isPageNumberPage = (page: PageData): boolean => {
    return page.items.length === 1 && page.items[0].isPageNumberOnly === true
}

const hasLongContent = (page: PageData): boolean => {
    return page.items.some(item => item.isLongTable === true)
}

const hasTableSections = (page: PageData): boolean => {
    return page.items.some(item => item.isTableSection === true)
}

// 跳转到指定页面
const goToPage = (pageId?: number) => {
    if (!pageId) return

    const pageIndex = pagesData.value.findIndex(page => page.id === pageId)
    if (pageIndex !== -1) {
        currentPageId.value = pageId
        // 滚动到页面顶部
        setTimeout(() => {
            const pageElement = document.querySelector(`[data-page-id="${pageId}"]`)
            if (pageElement) {
                pageElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
            }
        }, 100)
    }
}

// 调试页面数据
const debugCrossPageData = () => {
    console.log('=== 页面数据调试信息 ===')
    console.log('原始文档数据总数:', documentData.value.length)
    console.log('页面数据:', pagesData.value)

    // 统计各种页面类型
    const pageStats = {
        normal: 0,
        pageNumberOnly: 0,
        longTable: 0,
        totalPages: pagesData.value.length
    }

    pagesData.value.forEach(page => {
        if (isPageNumberPage(page)) {
            pageStats.pageNumberOnly++
            console.log(`页码页 ${page.id}: 高度=${page.height}px, 内容="${page.items[0]?.text}"`)
        } else if (hasLongContent(page)) {
            pageStats.longTable++
            const longItem = page.items.find(item => item.isLongTable)
            console.log(`长表格页 ${page.id}: 高度=${page.height}px, 表格长度=${longItem?.longTableInfo?.totalLength}字符`)
        } else {
            pageStats.normal++
            console.log(`普通页 ${page.id}: 高度=${page.height}px, 内容项=${page.items.length}个`)
        }
    })

    console.log('页面统计:', pageStats)

    // 显示长表格信息
    const longTables = documentData.value.filter(item =>
        item.type === 'table' && item.text.length > 8000
    )
    console.log(`发现 ${longTables.length} 个超长表格:`)
    longTables.forEach(table => {
        console.log(`- 页面${table.page_id}, 段落${table.paragraph_id}: ${table.text.length}字符`)
    })

    console.log('=== 调试信息结束 ===')
}

// 清除所有高亮
const clearAllHighlights = () => {
    // 清除建议高亮
    document.querySelectorAll('.suggestion-highlight').forEach(el => {
        el.classList.remove('suggestion-highlight')
    })

    // 清除激活的建议状态
    activeSuggestionId.value = ''

    console.log('已清除所有高亮')
}
</script>

<style lang="scss" scoped>
.md-reader-container {
    display: flex;
    height: 100vh;
    background: #f5f5f5;

    .sidebar {
        width: 300px;
        background: white;
        border-right: 1px solid #e8e8e8;
        display: flex;
        flex-direction: column;
        transition: width 0.3s ease;

        &.collapsed {
            width: 48px;
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;

            h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
            }
        }

        .toc-container {
            flex: 1;
            overflow-y: auto;
            padding: 8px 0;

            .toc-item {
                padding: 8px 16px;
                cursor: pointer;
                border-left: 3px solid transparent;
                transition: all 0.2s ease;
                font-size: 14px;
                line-height: 1.4;

                &:hover {
                    background: #f0f0f0;
                }

                &.active {
                    background: #e6f7ff;
                    border-left-color: #1890ff;
                    color: #1890ff;
                }

                &.level-0 {
                    font-weight: 600;
                    font-size: 16px;
                    color: #1890ff;
                }

                &.level-1 {
                    padding-left: 20px;
                    font-weight: 500;
                    font-size: 14px;
                }
            }
        }
    }

    .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;

        .toolbar {
            height: 56px;
            background: white;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;

            .toolbar-left,
            .toolbar-right {
                display: flex;
                align-items: center;
                gap: 12px;
            }
        }

        .content-container {
            flex: 1;
            overflow: auto;
            position: relative;
            width: 100%;
            height: 100%;
        }

        .zoom-wrapper {
            position: relative;
            min-height: 100%;
            overflow-x: auto;

            .loading-container {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;

                .loading-placeholder {
                    width: 100%;
                    height: 400px;
                }
            }

            .error-container {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
                padding: 40px;
            }

            .virtual-list {
                position: relative;

                .visible-content {
                    position: relative;
                }
            }
        }
    }
}

.page-container {
    background: white;
    margin: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-height: 800px;

    .page-header {
        padding: 16px 24px;
        border-bottom: 1px solid #e8e8e8;
        background: #fafafa;
        border-radius: 8px 8px 0 0;

        .page-number {
            font-weight: 600;
            color: #666;
        }
    }

    .page-content {
        padding: 24px;

        &.minimal-content {
            padding: 12px 24px;
            min-height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fafafa;
        }

        &.long-content {
            background: linear-gradient(to right, #fff2f0 0%, #ffffff 10%);
            border-left: 4px solid #ff7875;
        }

        &.table-sections {
            background: linear-gradient(to right, #e6f7ff 0%, #ffffff 10%);
            border-left: 4px solid #1890ff;
        }

        .page-number-notice {
            text-align: center;
            color: #999;

            .page-number-display {
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 8px;
                color: #666;
            }

            .page-type-hint {
                font-size: 12px;
                color: #999;
                background: #f0f0f0;
                padding: 4px 8px;
                border-radius: 4px;
                display: inline-block;
            }
        }

        .long-content-notice,
        .table-sections-notice {
            margin-bottom: 16px;
            padding: 8px 12px;
            border-radius: 4px;

            .long-content-indicator,
            .sections-indicator {
                display: flex;
                align-items: center;
                gap: 6px;
                font-size: 12px;
                font-weight: 500;

                svg {
                    flex-shrink: 0;
                }
            }
        }

        .long-content-notice {
            background: #fff2f0;
            border: 1px solid #ffccc7;

            .long-content-indicator {
                color: #ff7875;
            }
        }

        .table-sections-notice {
            background: #e6f7ff;
            border: 1px solid #91d5ff;

            .sections-indicator {
                color: #1890ff;
            }
        }

        .content-item {
            margin-bottom: 16px;
            position: relative;
            border-radius: 4px;
            transition: all 0.2s ease;

            &:hover {
                background: #f9f9f9;
            }

            &.highlighted {
                background: #fff7e6;
                border-left: 3px solid #ffa940;
            }

            &.editing {
                background: #e6f7ff;
                border: 1px solid #1890ff;
                padding: 12px;
            }

            &.type-paragraph {
                .paragraph-content {
                    .text-content {
                        line-height: 1.6;
                        font-size: 14px;
                        color: #333;

                        :deep(mark) {
                            padding: 2px 4px;
                            border-radius: 2px;
                        }
                    }

                    .edit-container {
                        .edit-actions {
                            margin-top: 8px;
                            display: flex;
                            gap: 8px;
                        }
                    }
                }
            }

            &.type-table {
                .table-content {
                    overflow-x: auto;

                    &.long-table {
                        border: 2px solid #ff7875;
                        border-radius: 8px;
                        background: linear-gradient(135deg, #fff2f0 0%, #ffffff 30%);
                        padding: 16px;
                        margin: 16px 0;
                    }

                    &.table-section {
                        border: 2px solid #1890ff;
                        border-radius: 8px;
                        background: linear-gradient(135deg, #e6f7ff 0%, #ffffff 30%);
                        padding: 16px;
                        margin: 12px 0;
                    }

                    &.table-main-part {
                        border: 2px solid #52c41a;
                        border-radius: 8px;
                        background: linear-gradient(135deg, #f6ffed 0%, #ffffff 30%);
                        padding: 16px;
                        margin: 12px 0;
                    }

                    &.table-continuation {
                        border: 2px solid #722ed1;
                        border-radius: 8px;
                        background: linear-gradient(135deg, #f9f0ff 0%, #ffffff 30%);
                        padding: 16px;
                        margin: 12px 0;
                    }

                    .table-info-banner {
                        margin: -16px -16px 16px -16px;
                        padding: 8px 16px;
                        border-radius: 6px 6px 0 0;
                        font-size: 12px;
                        font-weight: 500;

                        &.long-table-banner {
                            background: #ff7875;
                            color: white;
                        }

                        &.section-banner {
                            background: #1890ff;
                            color: white;
                        }

                        &.main-part-banner {
                            background: #52c41a;
                            color: white;
                        }

                        &.continuation-banner {
                            background: #722ed1;
                            color: white;
                        }

                        .banner-content {
                            display: flex;
                            align-items: center;
                            gap: 6px;

                            svg {
                                flex-shrink: 0;
                            }
                        }
                    }

                    :deep(table) {
                        width: 100%;
                        border-collapse: collapse;

                        td {
                            border: 1px solid #d9d9d9;
                            padding: 8px 12px;
                            font-size: 14px;
                            line-height: 1.4;
                        }

                        tr:nth-child(even) {
                            background: #fafafa;
                        }
                    }
                }
            }

            // 不同层级的样式
            &.level-0 {
                .text-content {
                    font-size: 24px;
                    font-weight: 700;
                    color: #1890ff;
                    text-align: center;
                    margin: 24px 0;
                }
            }

            &.level-1 {
                .text-content {
                    font-size: 20px;
                    font-weight: 600;
                    color: #262626;
                    margin: 20px 0 16px 0;
                    border-bottom: 2px solid #e8e8e8;
                    padding-bottom: 8px;
                }
            }

            &.level-2 {
                .text-content {
                    font-size: 18px;
                    font-weight: 600;
                    color: #262626;
                    margin: 16px 0 12px 0;
                }
            }

            &.level--1 {
                .text-content {
                    font-size: 14px;
                    line-height: 1.6;
                    color: #595959;
                    margin: 8px 0;
                }
            }
        }
    }
}

.color-picker {
    position: fixed;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 12px;
    z-index: 1000;

    .color-options {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;

        .color-option {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.2s ease;

            &:hover {
                border-color: #1890ff;
                transform: scale(1.1);
            }
        }
    }
}

.revision-panel {
    width: 400px;
    background: white;
    border-left: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
    transition: width 0.3s ease;

    &.collapsed {
        width: 0;
        overflow: hidden;
    }

    .revision-header {
        padding: 16px;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            gap: 4px;
        }
    }

    .revision-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .revision-stats {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .revision-filters {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .revision-list {
            flex: 1;
            overflow-y: auto;
            padding: 8px;

            .revision-item {
                background: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
                margin-bottom: 12px;
                padding: 12px;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                    border-color: #1890ff;
                    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
                }

                &.active {
                    border-color: #1890ff;
                    background: #e6f7ff;
                }

                &.type-add {
                    border-left: 4px solid #52c41a;
                }

                &.type-delete {
                    border-left: 4px solid #ff4d4f;
                }

                &.type-modify {
                    border-left: 4px solid #fa8c16;
                }

                .revision-item-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 8px;

                    .page-info {
                        font-size: 12px;
                        color: #666;
                    }
                }

                .revision-item-content {
                    margin-bottom: 12px;

                    .label {
                        font-size: 12px;
                        color: #666;
                        font-weight: 500;
                    }

                    .text {
                        font-size: 13px;
                        line-height: 1.4;
                        margin-left: 4px;

                        &.deleted {
                            background: #fff2f0;
                            color: #cf1322;
                            text-decoration: line-through;
                            padding: 2px 4px;
                            border-radius: 2px;
                        }

                        &.added {
                            background: #f6ffed;
                            color: #389e0d;
                            padding: 2px 4px;
                            border-radius: 2px;
                        }
                    }

                    .original-text,
                    .suggested-text {
                        margin-bottom: 4px;
                    }
                }

                .revision-item-actions {
                    margin-bottom: 8px;
                }

                .revision-item-reason {
                    font-size: 12px;
                    color: #666;
                    background: #fafafa;
                    padding: 6px 8px;
                    border-radius: 4px;

                    .label {
                        font-weight: 500;
                    }

                    .reason {
                        margin-left: 4px;
                    }
                }
            }
        }

        .suggestion-input {
            border-top: 1px solid #e8e8e8;
            padding: 16px;

            h4 {
                margin: 0 0 8px 0;
                font-size: 14px;
                font-weight: 600;
            }

            .input-actions {
                margin-top: 8px;
                text-align: right;
            }
        }
    }
}

// 建议高亮样式
.suggestion-highlight {
    background: #fff7e6 !important;
    border: 2px solid #ffa940 !important;
    border-radius: 4px;
    animation: highlight-pulse 0.5s ease-in-out;
}

@keyframes highlight-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.help-content {
    padding: 8px;
    min-width: 200px;

    h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 600;
    }

    p {
        margin: 4px 0;
        font-size: 12px;
        color: #666;
        line-height: 1.4;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .md-reader-container {
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 100;
            transform: translateX(-100%);
            transition: transform 0.3s ease;

            &:not(.collapsed) {
                transform: translateX(0);
            }
        }

        .revision-panel {
            position: fixed;
            right: 0;
            top: 0;
            height: 100vh;
            z-index: 100;
            transform: translateX(100%);
            transition: transform 0.3s ease;

            &:not(.collapsed) {
                transform: translateX(0);
            }
        }

        .main-content {
            margin-left: 0;
        }
    }

    .page-container {
        margin: 8px;

        .page-content {
            padding: 16px;
        }
    }
}

// 打印样式
@media print {
    .md-reader-container {
        .sidebar,
        .toolbar,
        .revision-panel {
            display: none;
        }

        .main-content {
            margin: 0;
        }

        .page-container {
            box-shadow: none;
            margin: 0;
            page-break-inside: avoid;
        }

        .suggestion-highlight {
            background: transparent !important;
            border: none !important;
        }
    }
}

/* 内容很少的页面样式 */
.page-content.minimal-content {
    min-height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.minimal-page-notice {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.page-number-display {
    font-size: 48px;
    font-weight: bold;
    color: #1890ff;
    margin-bottom: 16px;
}

.page-type-hint {
    font-size: 14px;
    color: #999;
    background: #f5f5f5;
    padding: 4px 12px;
    border-radius: 12px;
}

.content-item.page-number-only {
    display: none; /* 隐藏单独的页码数字，因为已经在上面显示了 */
}
</style>
