
page_id
integerrequired
当前元素所在页码，例如”1”

Example:
1

​
paragraph_id
integerrequired
当前元素id

​
outline_level
enum<integer>default:-1required
标题级别(最多支持5级标题) -1表示正文，0表示一级标题，1表示二级标题 …

Available options: -1, 0, 1, 2, 3, 4 
​
text
stringrequired
文本

Example:
"hello markdown"

​
position
integer[]required
以长度为8的整型数组表示四边形，8个数两两一组为一个点的横纵坐标，分别是左上，右上，右下，左下。
当输入是PDF时, 此坐标是基于72dpi的;当输入是图片时，此坐标是原图里的坐标。
单位：像素

Example:
[217, 390, 1336, 390, 1336, 460, 217, 460]
​
content
enum<integer>default:0required
内容类型

0 正文(段落、图片、表格)
1 非正文(页眉、页脚、侧边栏)
​
type
enum<string>default:paragraphrequired
类型, paragraph（段落类型，包括正文、标题、公式等文字信息）、image（图片类型）、table（表格类型）

Available options: image, table, paragraph 
​
origin_position
integer[]
仅当打开切边时返回，表示该段落在原图中的坐标。格式同position。

Example:
[217, 390, 1336, 390, 1336, 460, 217, 460]
​
sub_type
enum<string>
子类型。当type为paragraph时，取值范围为catalog(目录),header(页眉),footer(页脚),sidebar(侧边栏),text(正文普通文本),text_title(文本标题),image_title(图片标题),table_title(表格标题)；当type是image时，取值范围为stamp(印章),chart(图表),qrcode(二维码),barcode(条形码)；当type为table时，取值范围为bordered(有线表), borderless(无线表)。

Available options: catalog, header, footer, sidebar, text, text_title, image_title, table_title, stamp, chart, qrcode, barcode, bordered, borderless 
Example:
"catalog"

​
image_url
string
图片链接，仅在type为image时返回，当get_image = objects 时，返回图片的公共连接,图片默认保存30天，如需长久保存，请在有效期内下载图片并保存;或者使用image_output_type=base64str,图片以base64的方式返回

​
tags
string[]
表示段落内是否存在特殊文本，类型包括公式formula和手写体handwritten

Example:
["formula", "handwritten"]
​
caption_id
object
表格或图片的标题id，仅在type为image或table时返回

Show child attributes

​
cells
object[]
单元格数组, 仅在type为table时返回

Show child attributes

​
split_section_page_ids
integer[]
当表格/段落有合并时，记录合并前各个子表格/段落所在的页的id

Example:
[1, 2, 3]
​
split_section_positions
integer[][]
当表格/段落有合并时，记录合并前各个子表格/段落所在页的位置，位置所属的页码与split_section_page_ids按索引一一对应，如split_section_positions[2]所属的页码为split_section_page_ids[2]

Example:
[
  [0, 0, 100, 100, 100, 200, 0, 200],
  [0, 0, 100, 100, 100, 200, 0, 200],
  [0, 0, 100, 100, 100, 200, 0, 200]
]
​
stamp
object
当sub_type为stamp时，返回印章识别结果

