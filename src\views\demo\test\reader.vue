<template>
    <div class="reader-container"> 
        <div class="reader-content">  
        </div>
        <div class="other">  
        </div>
    </div>
</template>

<script setup lang='ts'>
import { ref, computed } from 'vue'
defineOptions({
    name: 'ReaderIndex'
}) 
</script>

<style lang="scss" scoped>
.reader-container {
    display: flex; 
    .reader-content {
        flex:1;
        min-width: 0;
        min-height: 0;
    }
    .other {
        width: 300px;
        flex-shrink: 0;
    }
}
</style>
