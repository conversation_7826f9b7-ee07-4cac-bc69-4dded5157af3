{"code": 200, "message": "success", "result": {"markdown": "# hello markdown", "detail": [{"page_id": 1, "paragraph_id": 123, "outline_level": -1, "text": "hello markdown", "position": [217, 390, 1336, 390, 1336, 460, 217, 460], "origin_position": [217, 390, 1336, 390, 1336, 460, 217, 460], "content": 0, "type": "paragraph", "sub_type": "catalog", "image_url": "<string>", "tags": ["formula", "handwritten"], "caption_id": {"page_id": 123, "paragraph_id": 123}, "cells": [{"row": 123, "col": 123, "row_span": 123, "col_span": 123, "position": [10, 10, 100, 10, 100, 50, 10, 50], "origin_position": [123], "text": "<string>", "type": "<string>"}], "split_section_page_ids": [1, 2, 3], "split_section_positions": [[0, 0, 100, 100, 100, 200, 0, 200], [0, 0, 100, 100, 100, 200, 0, 200], [0, 0, 100, 100, 100, 200, 0, 200]], "stamp": {"value": "<string>", "stamp_shape": "<string>", "type": "<string>", "color": "<string>"}}], "pages": [{"status": "success", "page_id": 0, "durations": 612.5, "image_id": "90u12adcad08r2", "origin_image_id": "90u12adcad08r2", "base64": "<string>", "origin_base64": "<string>", "width": 123, "height": 123, "angle": 123, "content": [{"id": 123, "type": "line", "text": "<string>", "angle": 0, "pos": [123], "origin_position": [123], "sub_type": "handwriting", "direction": 123, "score": 0.5, "char_pos": [[123]]}], "raw_ocr": [{"text": "这是一个例子。", "score": 0.99, "type": "text", "position": [10, 10, 100, 10, 100, 50, 10, 50], "angle": 123, "direction": 1, "handwritten": 1, "char_scores": [0.99, 0.98, 0.95, 0.95, 0.99, 0.93, 0.87], "char_centers": [[20, 10], [30, 10], [40, 10], [50, 10], [60, 10], [70, 10], [80, 10]], "char_positions": [[18, 8, 22, 8, 22, 12, 18, 12], [28, 88, 32, 8, 32, 12, 28, 12], [38, 88, 42, 8, 42, 12, 38, 12], [48, 88, 52, 8, 52, 12, 48, 12], [58, 88, 62, 8, 62, 12, 58, 12], [68, 88, 72, 8, 72, 12, 68, 12], [78, 88, 82, 8, 82, 12, 78, 12]], "char_candidates": [["这"], ["是"], ["一", "-"], ["个"], ["例"], ["子"], ["。", "O"]], "char_candidates_score": [[0.99], [0.99], [0.95, 0.05], [0.99], [0.99], [0.99], [0.89, 0.11]]}], "structured": [{"type": "textblock", "pos": [123], "origin_position": [123], "content": [0, 1, 2], "sub_type": "text", "continue": true, "next_page_id": 2, "next_para_id": 1, "text": "<string>", "outline_level": 123}]}], "catalog": {"toc": [[{"hierarchy": 2, "title": "1.公司简介和主要财务指标", "page_id": 3, "pos": [10, 10, 100, 10, 100, 50, 10, 50]}, {"hierarchy": 3, "title": "1.1 公司简介", "page_id": 4, "pos": [10, 10, 100, 10, 100, 50, 10, 50]}]]}, "total_page_number": 10, "valid_page_number": 3, "excel_base64": ""}, "version": "2.1.0", "duration": 999, "metrics": [{"page_image_width": 1024, "page_image_height": 768, "dpi": 72, "durations": 123, "status": "<string>", "page_id": 123, "angle": 90, "image_id": "<string>"}]}